import logging
from pathlib import Path

from pydantic import computed_field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


def _find_env_file() -> tuple[str | None, str]:
    """
    Find the appropriate .env file location with robust path resolution.

    Returns:
        Tuple of (env_file_path, debug_info) where env_file_path is None if not found
    """
    current_dir = Path.cwd()
    debug_info = f"Current working directory: {current_dir}"

    # Priority order for .env file locations
    search_paths = [
        # 1. Current directory (for when running from backend/)
        current_dir / ".env",
        # 2. Backend subdirectory (for when running from project root)
        current_dir / "backend" / ".env",
        # 3. Parent directory's backend subdirectory (for nested execution contexts)
        current_dir.parent / "backend" / ".env",
        # 4. Look for backend directory in current or parent directories
        current_dir / ".." / "backend" / ".env",
    ]

    debug_info += "\nSearching for .env file in order:"
    for i, path in enumerate(search_paths, 1):
        resolved_path = path.resolve()
        exists = resolved_path.exists()
        debug_info += f"\n  {i}. {resolved_path} - {'Found' if exists else 'Not found'}"

        if exists:
            debug_info += f"\nUsing .env file: {resolved_path}"
            return str(resolved_path), debug_info

    debug_info += "\nNo .env file found in any search location"
    return None, debug_info


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables or a .env file.
    """

    APP_TITLE: str = "Playlist Intelligence Agent MVP"
    API_VERSION: str = "0.1.0"
    LOG_LEVEL: str = "INFO"
    FRONTEND_URL: str = "http://localhost:5173"
    APP_ENV: str = "development"

    DB_USER: str = "admin"
    DB_PASSWORD: str = "password"
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_NAME: str = "playlist_agent_dev"
    DB_ECHO: bool = False

    SPOTIFY_CLIENT_ID: str | None = None
    SPOTIFY_CLIENT_SECRET: str | None = None
    SPOTIFY_REDIRECT_URI: str | None = None

    TOKENS_ENCRYPTION_KEY: str | None = None
    JWT_SECRET_KEY: str | None = None
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRY_MINUTES: int = 60

    @computed_field
    @property
    def DATABASE_URL_ASYNC(self) -> str:
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    @computed_field
    @property
    def DATABASE_URL_SYNC(self) -> str:
        return f"postgresql+psycopg2://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    @field_validator(
        "SPOTIFY_CLIENT_ID",
        "SPOTIFY_CLIENT_SECRET",
        "SPOTIFY_REDIRECT_URI",
        "TOKENS_ENCRYPTION_KEY",
        "JWT_SECRET_KEY",
    )
    @classmethod
    def check_required_settings(cls, v: str | None) -> str:
        if not v:
            raise ValueError("This environment variable must be set.")
        return v

    model_config = SettingsConfigDict(env_file=_find_env_file()[0], extra="ignore")

    def get_log_level(self) -> int:
        """Converts LOG_LEVEL string to logging level integer."""
        return getattr(logging, self.LOG_LEVEL.upper(), logging.INFO)


# Initialize settings with enhanced logging
def _create_settings() -> Settings:
    """Create Settings instance with enhanced .env file resolution logging."""
    env_file_path, debug_info = _find_env_file()

    # Log the .env file resolution process
    logger = logging.getLogger(__name__)

    if env_file_path:
        logger.info(
            "Configuration loading: .env file found",
            extra={"env_file_path": env_file_path, "resolution_details": debug_info},
        )
    else:
        logger.warning(
            "Configuration loading: No .env file found, using environment variables only",
            extra={
                "resolution_details": debug_info,
                "note": "This is expected in production environments",
            },
        )

    return Settings()


settings = _create_settings()
